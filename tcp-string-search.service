[Unit]
Description=TCP String Search Server
Documentation=file:///opt/tcp-string-search/README.md
After=network.target
Wants=network.target

[Service]
Type=simple
User=tcp-search
Group=tcp-search
WorkingDirectory=/opt/tcp-string-search
ExecStart=/usr/bin/python3 /opt/tcp-string-search/server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=tcp-string-search

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/tcp-string-search/logs
ReadOnlyPaths=/opt/tcp-string-search

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment
Environment=PYTHONPATH=/opt/tcp-string-search
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
