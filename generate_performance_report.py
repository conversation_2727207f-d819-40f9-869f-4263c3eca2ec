#!/usr/bin/env python3
"""
Enhanced Performance Report Generator for TCP String Search Server

This module generates comprehensive performance reports including:
- Professional PDF reports with charts and analysis
- Detailed CSV data exports
- Performance comparison across file sizes
- Load testing results analysis
- System resource utilization reports

Author: <PERSON>
Date: 2025
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import pandas as pd
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from generate_speed_report import generate_report as generate_algorithm_report
from performance_test_client import PerformanceTestClient, create_test_queries


class PerformanceReportGenerator:
    """Generate comprehensive performance reports."""
    
    def __init__(self, output_dir: str = "performance_reports"):
        """Initialize report generator."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create subdirectories
        (self.output_dir / "charts").mkdir(exist_ok=True)
        (self.output_dir / "data").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
    
    def run_server_performance_tests(self, host: str = "localhost", port: int = 8888, use_ssl: bool = True) -> Dict[str, Any]:
        """Run comprehensive server performance tests."""
        print("Running server performance tests...")

        # Try SSL first, then fall back to non-SSL
        client = None
        if use_ssl:
            try:
                client = PerformanceTestClient(host=host, port=port, use_ssl=True)
                if not client.test_connection():
                    print("SSL connection failed, trying non-SSL...")
                    client = None
            except Exception:
                print("SSL connection failed, trying non-SSL...")
                client = None

        if client is None:
            client = PerformanceTestClient(host=host, port=port, use_ssl=False)
            if not client.test_connection():
                raise ConnectionError(f"Cannot connect to server at {host}:{port}")
        
        results = {
            'timestamp': self.timestamp,
            'server': {'host': host, 'port': port},
            'tests': {}
        }
        
        # 1. Basic benchmark test
        print("  Running benchmark test...")
        queries = create_test_queries()
        benchmark_results = client.benchmark_queries(queries, iterations=20)
        results['tests']['benchmark'] = benchmark_results
        
        # 2. Concurrent load tests with different client counts
        print("  Running concurrent load tests...")
        load_test_results = []
        
        for client_count in [1, 5, 10, 25, 50]:
            print(f"    Testing with {client_count} concurrent clients...")
            try:
                load_result = client.concurrent_load_test(
                    queries[:8], client_count, duration_seconds=15
                )
                load_result['client_count'] = client_count
                load_test_results.append(load_result)
            except Exception as e:
                print(f"    Failed with {client_count} clients: {e}")
                break
        
        results['tests']['load_tests'] = load_test_results
        
        # 3. Sustained load test
        print("  Running sustained load test...")
        try:
            sustained_result = client.concurrent_load_test(
                queries[:5], concurrent_clients=10, duration_seconds=60
            )
            results['tests']['sustained_load'] = sustained_result
        except Exception as e:
            print(f"    Sustained load test failed: {e}")
            results['tests']['sustained_load'] = {'error': str(e)}
        
        return results
    
    def generate_performance_charts(self, results: Dict[str, Any]) -> List[str]:
        """Generate performance charts from test results."""
        chart_files = []
        
        # 1. Benchmark response time chart
        if 'benchmark' in results['tests']:
            chart_file = self.output_dir / "charts" / f"benchmark_response_times_{self.timestamp}.png"
            self._create_benchmark_chart(results['tests']['benchmark'], str(chart_file))
            chart_files.append(str(chart_file))
        
        # 2. Load test scaling chart
        if 'load_tests' in results['tests']:
            chart_file = self.output_dir / "charts" / f"load_test_scaling_{self.timestamp}.png"
            self._create_load_test_chart(results['tests']['load_tests'], str(chart_file))
            chart_files.append(str(chart_file))
        
        # 3. Sustained load chart
        if 'sustained_load' in results['tests'] and 'error' not in results['tests']['sustained_load']:
            chart_file = self.output_dir / "charts" / f"sustained_load_{self.timestamp}.png"
            self._create_sustained_load_chart(results['tests']['sustained_load'], str(chart_file))
            chart_files.append(str(chart_file))
        
        return chart_files
    
    def _create_benchmark_chart(self, benchmark_data: Dict[str, Any], output_file: str):
        """Create benchmark response time chart."""
        query_results = benchmark_data['query_results']
        
        queries = [r['query'][:20] + '...' if len(r['query']) > 20 else r['query'] for r in query_results]
        avg_times = [r['avg_time_ms'] for r in query_results]
        min_times = [r['min_time_ms'] for r in query_results]
        max_times = [r['max_time_ms'] for r in query_results]
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        x_pos = range(len(queries))
        
        # Create bar chart with error bars
        bars = ax.bar(x_pos, avg_times, yerr=[
            [avg - min_val for avg, min_val in zip(avg_times, min_times)],
            [max_val - avg for avg, max_val in zip(avg_times, max_times)]
        ], capsize=5, alpha=0.7, color='skyblue', edgecolor='navy')
        
        ax.set_xlabel('Queries')
        ax.set_ylabel('Response Time (ms)')
        ax.set_title('Benchmark Response Times by Query')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(queries, rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, avg_time in zip(bars, avg_times):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{avg_time:.2f}ms', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_load_test_chart(self, load_test_data: List[Dict[str, Any]], output_file: str):
        """Create load test scaling chart."""
        client_counts = [test['client_count'] for test in load_test_data]
        qps_values = [test['stats']['successful_qps'] for test in load_test_data]
        avg_response_times = [test['stats']['avg_response_time_ms'] for test in load_test_data]
        success_rates = [test['stats']['success_rate_percent'] for test in load_test_data]
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))
        
        # QPS chart
        ax1.plot(client_counts, qps_values, marker='o', linewidth=2, markersize=8, color='green')
        ax1.set_xlabel('Concurrent Clients')
        ax1.set_ylabel('Queries Per Second')
        ax1.set_title('Server Throughput vs Concurrent Clients')
        ax1.grid(True, alpha=0.3)
        
        # Response time chart
        ax2.plot(client_counts, avg_response_times, marker='s', linewidth=2, markersize=8, color='orange')
        ax2.set_xlabel('Concurrent Clients')
        ax2.set_ylabel('Average Response Time (ms)')
        ax2.set_title('Response Time vs Concurrent Clients')
        ax2.grid(True, alpha=0.3)
        
        # Success rate chart
        ax3.plot(client_counts, success_rates, marker='^', linewidth=2, markersize=8, color='red')
        ax3.set_xlabel('Concurrent Clients')
        ax3.set_ylabel('Success Rate (%)')
        ax3.set_title('Success Rate vs Concurrent Clients')
        ax3.set_ylim(0, 105)
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_sustained_load_chart(self, sustained_data: Dict[str, Any], output_file: str):
        """Create sustained load performance chart."""
        stats = sustained_data['stats']
        
        # Create a summary chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        metrics = ['QPS', 'Avg Response Time (ms)', 'Success Rate (%)']
        values = [
            stats['successful_qps'],
            stats['avg_response_time_ms'],
            stats['success_rate_percent']
        ]
        colors = ['green', 'orange', 'blue']
        
        bars = ax.bar(metrics, values, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title(f'Sustained Load Test Results\n'
                    f'Duration: {stats["actual_duration_seconds"]:.1f}s, '
                    f'Total Requests: {stats["total_requests"]:,}')
        ax.set_ylabel('Value')
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_markdown_report(self, results: Dict[str, Any], chart_files: List[str]) -> str:
        """Generate comprehensive markdown report."""
        report_file = self.output_dir / "reports" / f"performance_report_{self.timestamp}.md"
        
        with open(report_file, 'w') as f:
            f.write(f"# TCP String Search Server Performance Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Server:** {results['server']['host']}:{results['server']['port']}\n\n")
            
            # Executive Summary
            f.write("## Executive Summary\n\n")
            if 'benchmark' in results['tests']:
                stats = results['tests']['benchmark']['overall_stats']
                f.write(f"- **Average Response Time:** {stats['avg_response_time_ms']:.2f}ms\n")
                f.write(f"- **Success Rate:** {stats['success_rate_percent']:.1f}%\n")
                f.write(f"- **P95 Response Time:** {stats['p95_response_time_ms']:.2f}ms\n")
                f.write(f"- **P99 Response Time:** {stats['p99_response_time_ms']:.2f}ms\n\n")
            
            # Benchmark Results
            if 'benchmark' in results['tests']:
                f.write("## Benchmark Test Results\n\n")
                benchmark = results['tests']['benchmark']
                stats = benchmark['overall_stats']
                
                f.write(f"**Test Configuration:**\n")
                f.write(f"- Total Queries: {stats['total_queries']:,}\n")
                f.write(f"- Unique Query Patterns: {len(benchmark['query_results'])}\n")
                f.write(f"- Iterations per Query: {benchmark['query_results'][0]['iterations'] if benchmark['query_results'] else 'N/A'}\n\n")
                
                f.write(f"**Performance Metrics:**\n")
                f.write(f"- Average Response Time: {stats['avg_response_time_ms']:.2f}ms\n")
                f.write(f"- Minimum Response Time: {stats['min_response_time_ms']:.2f}ms\n")
                f.write(f"- Maximum Response Time: {stats['max_response_time_ms']:.2f}ms\n")
                f.write(f"- Median Response Time: {stats['median_response_time_ms']:.2f}ms\n")
                f.write(f"- P95 Response Time: {stats['p95_response_time_ms']:.2f}ms\n")
                f.write(f"- P99 Response Time: {stats['p99_response_time_ms']:.2f}ms\n")
                f.write(f"- Success Rate: {stats['success_rate_percent']:.1f}%\n\n")
            
            # Load Test Results
            if 'load_tests' in results['tests']:
                f.write("## Load Test Results\n\n")
                f.write("| Concurrent Clients | QPS | Avg Response Time (ms) | Success Rate (%) |\n")
                f.write("|---------------------|-----|------------------------|------------------|\n")
                
                for test in results['tests']['load_tests']:
                    stats = test['stats']
                    f.write(f"| {test['client_count']:>18} | {stats['successful_qps']:>3.1f} | "
                           f"{stats['avg_response_time_ms']:>20.2f} | {stats['success_rate_percent']:>15.1f}% |\n")
                f.write("\n")
            
            # Sustained Load Results
            if 'sustained_load' in results['tests'] and 'error' not in results['tests']['sustained_load']:
                f.write("## Sustained Load Test Results\n\n")
                stats = results['tests']['sustained_load']['stats']
                f.write(f"- **Duration:** {stats['actual_duration_seconds']:.1f} seconds\n")
                f.write(f"- **Total Requests:** {stats['total_requests']:,}\n")
                f.write(f"- **Successful Requests:** {stats['successful_requests']:,}\n")
                f.write(f"- **Failed Requests:** {stats['failed_requests']:,}\n")
                f.write(f"- **Average QPS:** {stats['successful_qps']:.1f}\n")
                f.write(f"- **Average Response Time:** {stats['avg_response_time_ms']:.2f}ms\n")
                f.write(f"- **Success Rate:** {stats['success_rate_percent']:.1f}%\n\n")
            
            # Charts
            f.write("## Performance Charts\n\n")
            for chart_file in chart_files:
                chart_name = Path(chart_file).name
                f.write(f"![{chart_name}](../charts/{chart_name})\n\n")
            
            # Recommendations
            f.write("## Performance Analysis and Recommendations\n\n")
            
            if 'benchmark' in results['tests']:
                stats = results['tests']['benchmark']['overall_stats']
                
                if stats['avg_response_time_ms'] <= 1.0:
                    f.write("✅ **Excellent Response Time:** Average response time is under 1ms.\n\n")
                elif stats['avg_response_time_ms'] <= 10.0:
                    f.write("✅ **Good Response Time:** Average response time is acceptable.\n\n")
                else:
                    f.write("⚠️ **High Response Time:** Consider optimizing search algorithms or server configuration.\n\n")
                
                if stats['success_rate_percent'] >= 99.0:
                    f.write("✅ **Excellent Reliability:** Success rate is above 99%.\n\n")
                elif stats['success_rate_percent'] >= 95.0:
                    f.write("✅ **Good Reliability:** Success rate is acceptable.\n\n")
                else:
                    f.write("⚠️ **Reliability Issues:** Investigate causes of failed requests.\n\n")
        
        return str(report_file)
    
    def save_raw_data(self, results: Dict[str, Any]) -> str:
        """Save raw test data to JSON file."""
        data_file = self.output_dir / "data" / f"raw_performance_data_{self.timestamp}.json"
        
        with open(data_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        return str(data_file)
    
    def generate_full_report(self, host: str = "localhost", port: int = 8888, use_ssl: bool = True) -> Dict[str, str]:
        """Generate complete performance report with all components."""
        print(f"Generating comprehensive performance report for {host}:{port}")
        print(f"Output directory: {self.output_dir}")

        # Run performance tests
        results = self.run_server_performance_tests(host, port, use_ssl)
        
        # Generate charts
        chart_files = self.generate_performance_charts(results)
        
        # Generate reports
        markdown_report = self.generate_markdown_report(results, chart_files)
        raw_data_file = self.save_raw_data(results)
        
        # Generate algorithm comparison report (from existing module)
        print("Generating algorithm comparison report...")
        try:
            generate_algorithm_report()
            algorithm_report = "docs/speed_report.md"
        except Exception as e:
            print(f"Algorithm report generation failed: {e}")
            algorithm_report = None
        
        report_files = {
            'markdown_report': markdown_report,
            'raw_data': raw_data_file,
            'charts': chart_files,
        }
        
        if algorithm_report:
            report_files['algorithm_report'] = algorithm_report
        
        print(f"\nPerformance report generation complete!")
        print(f"Main report: {markdown_report}")
        print(f"Raw data: {raw_data_file}")
        print(f"Charts: {len(chart_files)} files generated")
        
        return report_files


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate comprehensive performance report")
    parser.add_argument('--host', default='localhost', help='Server host')
    parser.add_argument('--port', type=int, default=8888, help='Server port')
    parser.add_argument('--output-dir', default='performance_reports', help='Output directory')
    parser.add_argument('--ssl', action='store_true', help='Use SSL connection (default: auto-detect)')
    parser.add_argument('--no-ssl', action='store_true', help='Force non-SSL connection')

    args = parser.parse_args()

    # Determine SSL usage
    if args.no_ssl:
        use_ssl = False
    elif args.ssl:
        use_ssl = True
    else:
        use_ssl = True  # Default to trying SSL first

    generator = PerformanceReportGenerator(args.output_dir)
    report_files = generator.generate_full_report(args.host, args.port, use_ssl)
    
    print(f"\nGenerated files:")
    for report_type, file_path in report_files.items():
        if isinstance(file_path, list):
            print(f"  {report_type}: {len(file_path)} files")
        else:
            print(f"  {report_type}: {file_path}")


if __name__ == "__main__":
    main()
