#!/usr/bin/env python3
"""
TCP String Search Server - Installation Testing Script

This script tests both system-wide and user-space installations to ensure
they work correctly and don't interfere with each other.

Author: <PERSON>
Date: 2025
"""

import os
import sys
import time
import socket
import subprocess
import threading
from pathlib import Path
from typing import Optional, Tuple


def print_header(title: str) -> None:
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_status(message: str, status: bool) -> None:
    """Print a status message with colored indicator."""
    indicator = "✅" if status else "❌"
    print(f"{indicator} {message}")


def test_tcp_connection(host: str = "localhost", port: int = 8888, timeout: int = 5) -> Tuple[bool, str]:
    """Test TCP connection to server."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            if result == 0:
                # Test a simple query
                sock.send(b"test\n")
                response = sock.recv(1024).decode().strip()
                return True, f"Connected successfully, response: {response}"
            else:
                return False, f"Connection failed with code: {result}"
    except Exception as e:
        return False, f"Connection error: {e}"


def test_system_installation() -> bool:
    """Test system-wide installation."""
    print_header("Testing System-Wide Installation")
    
    # Check if service is running
    try:
        result = subprocess.run(
            ["sudo", "systemctl", "is-active", "tcp-string-search"],
            capture_output=True, text=True, timeout=10
        )
        service_active = result.stdout.strip() == "active"
        print_status(f"System service active", service_active)
        
        if service_active:
            # Test connection
            connected, message = test_tcp_connection()
            print_status(f"TCP connection test: {message}", connected)
            return connected
        else:
            print_status("System service not active", False)
            return False
            
    except subprocess.TimeoutExpired:
        print_status("Service check timed out", False)
        return False
    except Exception as e:
        print_status(f"Service check failed: {e}", False)
        return False


def test_user_installation() -> bool:
    """Test user-space installation."""
    print_header("Testing User-Space Installation")
    
    # Check if user installation exists
    user_install_dir = Path.home() / ".local" / "tcp-string-search"
    install_exists = user_install_dir.exists()
    print_status(f"User installation directory exists", install_exists)
    
    if not install_exists:
        return False
    
    # Check virtual environment
    venv_dir = user_install_dir / "venv"
    venv_exists = venv_dir.exists()
    print_status(f"Virtual environment exists", venv_exists)
    
    # Check wrapper scripts
    bin_dir = Path.home() / ".local" / "bin"
    wrapper_scripts = [
        "tcp-string-search-server",
        "tcp-string-search-client",
        "tcp-string-search-perf",
        "tcp-string-search-gencerts"
    ]
    
    scripts_exist = all((bin_dir / script).exists() for script in wrapper_scripts)
    print_status(f"Wrapper scripts exist", scripts_exist)
    
    # Check user service
    try:
        result = subprocess.run(
            ["systemctl", "--user", "is-active", "tcp-string-search"],
            capture_output=True, text=True, timeout=10
        )
        user_service_active = result.stdout.strip() == "active"
        print_status(f"User service active", user_service_active)
    except Exception:
        user_service_active = False
        print_status("User service check failed", False)
    
    return install_exists and venv_exists


def create_wrapper_scripts() -> bool:
    """Create missing wrapper scripts for user installation."""
    print_header("Creating Missing Wrapper Scripts")
    
    user_install_dir = Path.home() / ".local" / "tcp-string-search"
    bin_dir = Path.home() / ".local" / "bin"
    venv_dir = user_install_dir / "venv"
    
    if not user_install_dir.exists():
        print_status("User installation directory not found", False)
        return False
    
    # Create bin directory if it doesn't exist
    bin_dir.mkdir(parents=True, exist_ok=True)
    
    # Wrapper script templates
    scripts = {
        "tcp-string-search-server": f"""#!/bin/bash
# TCP String Search Server Wrapper Script
cd "{user_install_dir}"
source "{venv_dir}/bin/activate"
exec python3 server.py "$@"
""",
        "tcp-string-search-client": f"""#!/bin/bash
# TCP String Search Client Wrapper Script
cd "{user_install_dir}"
source "{venv_dir}/bin/activate"
exec python3 client.py "$@"
""",
        "tcp-string-search-perf": f"""#!/bin/bash
# TCP String Search Performance Test Wrapper Script
cd "{user_install_dir}"
source "{venv_dir}/bin/activate"
exec python3 performance_test_client.py "$@"
""",
        "tcp-string-search-gencerts": f"""#!/bin/bash
# TCP String Search Certificate Generation Wrapper Script
cd "{user_install_dir}"
source "{venv_dir}/bin/activate"
exec python3 generate_certs.py "$@"
"""
    }
    
    success = True
    for script_name, script_content in scripts.items():
        script_path = bin_dir / script_name
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            os.chmod(script_path, 0o755)
            print_status(f"Created: {script_name}", True)
        except Exception as e:
            print_status(f"Failed to create {script_name}: {e}", False)
            success = False
    
    return success


def test_user_commands() -> bool:
    """Test user-space commands."""
    print_header("Testing User-Space Commands")
    
    # Test if commands are available
    commands = [
        "tcp-string-search-server",
        "tcp-string-search-client", 
        "tcp-string-search-perf",
        "tcp-string-search-gencerts"
    ]
    
    all_available = True
    for cmd in commands:
        try:
            result = subprocess.run(
                ["which", cmd], capture_output=True, text=True, timeout=5
            )
            available = result.returncode == 0
            print_status(f"Command '{cmd}' available", available)
            all_available &= available
        except Exception as e:
            print_status(f"Command '{cmd}' check failed: {e}", False)
            all_available = False
    
    return all_available


def test_client_functionality() -> bool:
    """Test client functionality against running server."""
    print_header("Testing Client Functionality")
    
    # Test basic client connection
    try:
        result = subprocess.run(
            ["python3", "client.py", "--query", "test", "--timeout", "5"],
            capture_output=True, text=True, timeout=10, cwd="."
        )
        
        client_works = result.returncode == 0
        response = result.stdout.strip() if result.stdout else result.stderr.strip()
        print_status(f"Client test: {response}", client_works)
        
        return client_works
        
    except subprocess.TimeoutExpired:
        print_status("Client test timed out", False)
        return False
    except Exception as e:
        print_status(f"Client test failed: {e}", False)
        return False


def cleanup_user_installation() -> bool:
    """Clean up redundant files in user installation."""
    print_header("Cleaning Up User Installation")
    
    user_install_dir = Path.home() / ".local" / "tcp-string-search"
    if not user_install_dir.exists():
        print_status("User installation not found", False)
        return False
    
    # Run cleanup in user installation directory
    cleanup_script = user_install_dir / "cleanup.sh"
    if cleanup_script.exists():
        try:
            # Copy our cleanup script to user installation
            import shutil
            shutil.copy2("cleanup.sh", str(cleanup_script))
            os.chmod(cleanup_script, 0o755)
            
            # Run cleanup with automatic 'y' response
            result = subprocess.run(
                ["bash", str(cleanup_script)],
                input="y\n", text=True, capture_output=True,
                cwd=str(user_install_dir), timeout=30
            )
            
            success = result.returncode == 0
            print_status("User installation cleanup", success)
            return success
            
        except Exception as e:
            print_status(f"Cleanup failed: {e}", False)
            return False
    else:
        print_status("Cleanup script not found", False)
        return False


def main() -> None:
    """Main testing function."""
    print("TCP String Search Server - Installation Testing")
    print("Author: Brian Kimathi")
    print("Date: 2025")
    
    # Test results
    results = {}
    
    # Test system installation
    results["System Installation"] = test_system_installation()
    
    # Test user installation
    results["User Installation"] = test_user_installation()
    
    # Create missing wrapper scripts if needed
    if not results["User Installation"]:
        print_status("Attempting to fix user installation...", True)
        create_wrapper_scripts()
        results["User Installation"] = test_user_installation()
    
    # Test user commands
    results["User Commands"] = test_user_commands()
    
    # Test client functionality
    results["Client Functionality"] = test_client_functionality()
    
    # Clean up user installation
    results["User Cleanup"] = cleanup_user_installation()
    
    # Summary
    print_header("Installation Testing Summary")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDetailed results:")
    for test_name, result in results.items():
        print_status(test_name, result)
    
    if all(results.values()):
        print(f"\n🎉 ALL TESTS PASSED! Both installations are working correctly.")
        print(f"🚀 The TCP String Search Server is ready for production deployment.")
    else:
        print(f"\n⚠️  Some tests failed. Please review the issues above.")
    
    # Exit with appropriate code
    sys.exit(0 if all(results.values()) else 1)


if __name__ == "__main__":
    main()
