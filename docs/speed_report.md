# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |    50K |   100K |   250K |
|:------------------------------|------:|-------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.003 |  0.004 |  0.005 |  0.004 |
| Hash Set (FrozenSet)          | 0.001 |  0.001 |  0.001 |  0.002 |
| Hash Set (FrozenSet) (Reread) | 0.024 |  0.029 |  0.068 |  0.051 |
| Linear Search (Optimized)     | 1.715 |  9.467 | 23.98  | 30.965 |
| Memory-Mapped                 | 2.998 | 12.478 | 27.806 | 39.43  |
| Native Grep                   | 4.437 |  5.206 |  8.159 |  9.826 |

### Load Time by File Size

| Algorithm                     |    10K |    50K |    100K |    250K |
|:------------------------------|-------:|-------:|--------:|--------:|
| Binary Search (Deduplicated)  | 15.55  | 48.515 | 101.814 | 219     |
| Hash Set (FrozenSet)          |  4.551 | 27.378 |  77.903 | 191.487 |
| Hash Set (FrozenSet) (Reread) |  3.863 | 34.58  |  66.848 | 176.221 |
| Linear Search (Optimized)     |  0.002 |  0.001 |   0.004 |   0.002 |
| Memory-Mapped                 |  0.081 |  0.165 |   0.222 |   0.517 |
| Native Grep                   |  0.002 |  0.002 |   0.002 |   0.002 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
